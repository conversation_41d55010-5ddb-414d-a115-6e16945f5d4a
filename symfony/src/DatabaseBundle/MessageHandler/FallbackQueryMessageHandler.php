<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\MessageHandler;

use LoginAutonom\ApproverBundle\Interfaces\ApproverProcessIdsAwareInterface;
use LoginAutonom\AuthBundle\Guesser\ProcessIdsFromRequestToClassCheckingGuesser;
use LoginAutonom\AuthBundle\Provider\ProcessIdsFromRequestToClassProvider;
use LoginAutonom\CoreBundle\Storage\ContextStorage;
use LoginAutonom\CoreBundle\Util\ResultExtractorFromMessageEnvelope;
use LoginAutonom\DatabaseBundle\Builder\DatabaseQueryBusStampStorageBuilder;
use LoginAutonom\DatabaseBundle\Builder\DatabaseQueryDescriptorBuilder;
use LoginAutonom\DatabaseBundle\Handler\QueryBuilderModifyHandler;
use LoginAutonom\DatabaseBundle\Interfaces\DatabaseQueryMessageInterface;
use LoginAutonom\DatabaseBundle\Interfaces\QueryBuilderServiceInterface;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\ApproverProcessIdsStamp;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoVisibilityPermissionStamp;
use LoginAutonom\DatabaseBundle\QueryBuilder\FallbackQueryBuilder;
use LoginAutonom\DatabaseBundle\Storage\StampStorage;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\Service\ServiceProviderInterface;

#[AsMessageHandler(priority: 1000)]
final readonly class FallbackQueryMessageHandler
{
    public function __construct(
        #[TaggedLocator(tag: QueryBuilderServiceInterface::TAG, defaultIndexMethod: 'getHandledQueryClass')]
        private ServiceProviderInterface $queryBuilders,
        private ResultExtractorFromMessageEnvelope $extractor,
        private MessageBusInterface $databaseQueryBus,
        private QueryBuilderModifyHandler $queryBuilderModifyHandler,
        private LoggerInterface $logger,
        private DatabaseQueryDescriptorBuilder $databaseQueryDescriptorBuilder,
        private ContextStorage $contextStorage,
        private ProcessIdsFromRequestToClassCheckingGuesser $processIdsFromRequestToClassCheckingGuesser,
        private ProcessIdsFromRequestToClassProvider $processIdsFromRequestToClassProvider,
        private FallbackQueryBuilder $fallbackQueryBuilder,
        private DatabaseQueryBusStampStorageBuilder $databaseQueryBusStampStorageBuilder,
    ) {
    }

    public function __invoke(DatabaseQueryMessageInterface $query, StampStorage $storage)
    {
        /** @var QueryBuilderServiceInterface $queryBuildingBuilder */
        $queryClass = get_class($query);
        if ($this->queryBuilders->has($queryClass)) {
            $queryBuildingBuilder = $this->queryBuilders->get($queryClass);
        } else {
            $queryBuildingBuilder = $this->fallbackQueryBuilder;
        }
        $this->logger->debug('Fallback: ' . $queryClass . ' -> ' . get_class($queryBuildingBuilder));

        if (!$storage->has(NoVisibilityPermissionStamp::class)) {
            $this->setProcessIdsToStampStorage($query, $storage);
        }
        if (method_exists($queryBuildingBuilder, 'setStamps')) {
            $queryBuildingBuilder->setStamps($storage);
        }
        $queryBuilder = $queryBuildingBuilder->build($query);
        $this->queryBuilderModifyHandler->handle($queryBuilder, $storage->getFlattenStamps());

        $databaseMessage = ($this->databaseQueryDescriptorBuilder)
            ->reset()
            ->setQueryMessage($query)
            ->setStampStorage($storage)
            ->setQueryBuilder($queryBuilder)
            ->build();

        $databaseQueryStampStorage = $this->databaseQueryBusStampStorageBuilder
            ->reset()
            ->setQueryStampStorage($storage)
            ->setQuery($query)
            ->build();

        $envelope = $this->databaseQueryBus->dispatch(
            $databaseMessage,
            $databaseQueryStampStorage->getFlattenStamps()
        );

        return $this->extractor->extract($envelope);
    }

    private function setProcessIdsToStampStorage(DatabaseQueryMessageInterface $query, StampStorage $storage): void
    {
        if ($this->haProcessIdInMessage($query)) {
            $storage->addStamp(
                new ApproverProcessIdsStamp($query->getProcessIds())
            );
        } elseif ($this->hasProcessIdWithClass($query)) {
            $storage->addStamp(
                new ApproverProcessIdsStamp(
                    $this->processIdsFromRequestToClassProvider->provide(
                        $this->contextStorage->getRequestName(),
                        get_class($query),
                    )
                )
            );
        } elseif ($this->contextStorage->hasProcessIds()) {
            $storage->addStamp(
                new ApproverProcessIdsStamp(
                    $this->contextStorage->getProcessIds()
                )
            );
        }
    }

    private function haProcessIdInMessage(DatabaseQueryMessageInterface $query): bool
    {
        return $query instanceof ApproverProcessIdsAwareInterface
            && $query->hasProcessId();
    }

    private function hasProcessIdWithClass(DatabaseQueryMessageInterface $query): bool
    {
        return $this->contextStorage->hasRequestName()
            && $this->processIdsFromRequestToClassCheckingGuesser->guess(
                $this->contextStorage->getRequestName(),
                get_class($query)
            );
    }
}
