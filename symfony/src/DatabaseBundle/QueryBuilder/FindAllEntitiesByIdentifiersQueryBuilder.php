<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\QueryBuilder;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\DatabaseBundle\Event\Query\FindAllEntitiesByIdentifiersQuery;
use LoginAutonom\DatabaseBundle\Interfaces\DatabaseQueryMessageInterface;

final class FindAllEntitiesByIdentifiersQueryBuilder extends AbstractQueryBuilder
{
    public const ALIAS = 'entityClass';

    public function build(DatabaseQueryMessageInterface $query): QueryBuilder
    {
        /** @var FindAllEntitiesByIdentifiersQuery $query */
        $qb = $this->util->createQueryBuilder();
        $qb->select(self::ALIAS)
            ->from($query->getEntityClass(), self::ALIAS);
        $expressions = [];
        $entityNum = 0;
        foreach ($query->getIdentifiers() as $entityIdentifiers) {
            $entityExpressions = [];
            foreach ($entityIdentifiers as $identifierName => $identifierValue) {
                $variableName = ":{$identifierName}_{$entityNum}";
                $entityExpressions[] = $qb->expr()->eq(self::ALIAS . ".$identifierName", $variableName);
                $qb->setParameter($variableName, $identifierValue);
            }
            $expressions[] = $qb->expr()->andX(...$entityExpressions);
            $entityNum++;
        }
        $qb = $qb->andWhere(
            $qb->expr()->orX(...$expressions)
        );

        return $qb;
    }

    public static function getHandledQueryClass(): string
    {
        return FindAllEntitiesByIdentifiersQuery::class;
    }
}
