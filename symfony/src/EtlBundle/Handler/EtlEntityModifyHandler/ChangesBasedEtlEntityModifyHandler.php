<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Handler\EtlEntityModifyHandler;

use LoginAutonom\CoreBundle\DTO\SingleValidity;
use LoginAutonom\DatabaseBundle\Builder\EntityCloneBuilder;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\ValidityEmbeddable;
use LoginAutonom\DatabaseBundle\Handler\EntityChangePlanHandler;
use LoginAutonom\DatabaseBundle\Handler\ValidityIntervalBasedEntitiesHandler;
use LoginAutonom\EtlBundle\DTO\EtlEntityModified;
use LoginAutonom\EtlBundle\DTO\EtlEntityModifyInfo;
use LoginAutonom\EtlBundle\Interfaces\EtlEntityModifyHandlerInterface;

final readonly class ChangesBasedEtlEntityModifyHandler implements EtlEntityModifyHandlerInterface
{
    public function __construct(
        private EntityChangePlanHandler $changePlanHandler,
        private ValidityIntervalBasedEntitiesHandler $validityIntervalBasedEntitiesHandler,
        private EntityCloneBuilder $entityCloneBuilder,
    ) {
    }

    public function modify(EtlEntityModifyInfo $info): EtlEntityModified
    {
        $entity = $info->getEntity();
        $mapping = $info->getMapping();
        $objectStorage = $mapping->getValidityBasedObject(get_class($entity));
        $changePlan = $info->getPlan();
        $newEntity = $this->entityCloneBuilder->reset()
            ->setEntity($entity)
            ->build();
        $newEntity = $this->changePlanHandler->handle($newEntity, $changePlan);
        /** @var SingleValidity $validity */
        $validity = $info->getValidity();
        $newEntity->setValidity(
            (new ValidityEmbeddable())
            ->setValidFrom($validity->getValidity())
        );
        $objectChanges = $this->validityIntervalBasedEntitiesHandler->attachNewValidity($newEntity, $objectStorage);

        return new EtlEntityModified(
            $newEntity,
            $objectChanges
        );
    }

    public static function getName(): string
    {
        return 'changes-based-etl-entity-modify-handler';
    }
}
