<?php

declare(strict_types=1);

namespace LoginAutonom\Tests\PHPUnit\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Row\Factory\NativeEntryFactory;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\Builder\EntityChangePlanBuilder;
use LoginAutonom\DatabaseBundle\DTO\EntityChangePlan;
use LoginAutonom\DatabaseBundle\DTO\ObjectsChanges;
use LoginAutonom\DatabaseBundle\Provider\MultiEntitiesByIdentifiersProvider;
use LoginAutonom\EtlBundle\DTO\EtlEntityModified;
use LoginAutonom\EtlBundle\DTO\EtlValidityBasedEntityCacheWithMapping;
use LoginAutonom\EtlBundle\Enum\EtlCommonFieldNamesEnum;
use LoginAutonom\EtlBundle\Flow\Transformer\UpdateValidityBasedEntityTransformer;
use LoginAutonom\EtlBundle\Interfaces\EtlEntityModifyHandlerInterface;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class UpdateValidityBasedEntityTransformerTest extends TestCase
{
    private NativeEntryFactory $entryFactory;
    private FlowContext $context;
    private LoggerInterface $logger;
    private EntityChangePlanBuilder $changePlanBuilder;
    private EtlEntityModifyHandlerInterface $modifyHandler;
    private MultiEntitiesByIdentifiersProvider $entitiesByIdentifiersProvider;
    private EtlValidityBasedEntityCacheWithMapping $entityCache;

    protected function setUp(): void
    {
        $this->entryFactory = new NativeEntryFactory();
        $this->context = $this->createMock(FlowContext::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->changePlanBuilder = $this->createMock(EntityChangePlanBuilder::class);
        $this->modifyHandler = $this->createMock(EtlEntityModifyHandlerInterface::class);
        $this->entitiesByIdentifiersProvider = $this->createMock(MultiEntitiesByIdentifiersProvider::class);
        $this->entityCache = $this->createMock(EtlValidityBasedEntityCacheWithMapping::class);

        $this->context->method('entryFactory')->willReturn($this->entryFactory);
    }

    public function testTransformWithValidityInterval(): void
    {
        $fieldMap = ['name' => 'name', 'description' => 'description'];
        $fromField = 'valid_from';
        $toField = 'valid_to';
        $identifierFields = ['id' => 'entity_id'];
        $entityClass = 'TestEntity';

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->changePlanBuilder,
            $this->modifyHandler,
            $this->entitiesByIdentifiersProvider,
            $toField
        );
        $transformer->setLogger($this->logger);

        $entity = new \stdClass();
        $entity->name = 'Old Name';
        $entity->description = 'Old Description';

        $changesStorage = new ObjectsChanges();
        $validFrom = new \DateTime('2023-01-01');
        $validTo = new \DateTime('2023-12-31');

        $row = Row::create(
            $this->entryFactory->create('entity_id', 123),
            $this->entryFactory->create('name', 'New Name'),
            $this->entryFactory->create('description', 'New Description'),
            $this->entryFactory->create($fromField, $validFrom),
            $this->entryFactory->create($toField, $validTo),
            $this->entryFactory->create(EtlCommonFieldNamesEnum::CHANGES_STORAGE, $changesStorage),
            $this->entryFactory->create(EtlCommonFieldNamesEnum::ENTITY_CACHE, $this->entityCache)
        );

        // Mock entity cache to return entity
        $this->entityCache->method('get')
            ->with(['id' => 123], $entityClass)
            ->willReturn($this->createValidityBasedEntityMock([$entity]));

        // Mock change plan builder
        $changePlan = $this->createMock(EntityChangePlan::class);
        $changePlan->method('hasChanges')->willReturn(true);

        $this->changePlanBuilder->method('reset')->willReturnSelf();
        $this->changePlanBuilder->method('setEntity')->willReturnSelf();
        $this->changePlanBuilder->method('setFieldMap')->willReturnSelf();
        $this->changePlanBuilder->method('setFields')->willReturnSelf();
        $this->changePlanBuilder->method('build')->willReturn($changePlan);

        // Mock modify handler
        $modifiedEntity = new \stdClass();
        $modifiedEntity->name = 'New Name';
        $modifiedEntity->description = 'New Description';

        $entityChanges = new EtlEntityModified($modifiedEntity, new ObjectsChanges());
        $this->modifyHandler->method('modify')->willReturn($entityChanges);

        $result = $transformer->transform($row, $this->context);

        $this->assertSame($row, $result);
        $this->assertTrue($changesStorage->hasChanged());
    }

    public function testTransformWithSingleValidity(): void
    {
        $fieldMap = ['name' => 'name'];
        $fromField = 'valid_date';
        $identifierFields = ['id' => 'entity_id'];
        $entityClass = 'TestEntity';

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->changePlanBuilder,
            $this->modifyHandler,
            $this->entitiesByIdentifiersProvider
        );
        $transformer->setLogger($this->logger);

        $entity = new \stdClass();
        $entity->name = 'Old Name';

        $changesStorage = new ObjectsChanges();
        $validDate = new \DateTime('2023-06-15');

        $row = Row::create(
            $this->entryFactory->create('entity_id', 456),
            $this->entryFactory->create('name', 'Updated Name'),
            $this->entryFactory->create($fromField, $validDate),
            $this->entryFactory->create(EtlCommonFieldNamesEnum::CHANGES_STORAGE, $changesStorage),
            $this->entryFactory->create(EtlCommonFieldNamesEnum::ENTITY_CACHE, $this->entityCache)
        );

        // Mock entity cache to return entity
        $this->entityCache->method('get')
            ->with(['id' => 456], $entityClass)
            ->willReturn($this->createValidityBasedEntityMock([$entity]));

        // Mock change plan builder
        $changePlan = $this->createMock(EntityChangePlan::class);
        $changePlan->method('hasChanges')->willReturn(true);

        $this->changePlanBuilder->method('reset')->willReturnSelf();
        $this->changePlanBuilder->method('setEntity')->willReturnSelf();
        $this->changePlanBuilder->method('setFieldMap')->willReturnSelf();
        $this->changePlanBuilder->method('setFields')->willReturnSelf();
        $this->changePlanBuilder->method('build')->willReturn($changePlan);

        // Mock modify handler
        $modifiedEntity = new \stdClass();
        $modifiedEntity->name = 'Updated Name';

        $entityChanges = new EtlEntityModified($modifiedEntity, new ObjectsChanges());
        $this->modifyHandler->method('modify')->willReturn($entityChanges);

        $result = $transformer->transform($row, $this->context);

        $this->assertSame($row, $result);
        $this->assertTrue($changesStorage->hasChanged());
    }

    public function testTransformWithNoEntitiesFound(): void
    {
        $fieldMap = ['name' => 'name'];
        $fromField = 'valid_date';
        $identifierFields = ['id' => 'entity_id'];
        $entityClass = 'TestEntity';

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->changePlanBuilder,
            $this->modifyHandler,
            $this->entitiesByIdentifiersProvider
        );
        $transformer->setLogger($this->logger);

        $changesStorage = new ObjectsChanges();
        $validDate = new \DateTime('2023-06-15');

        $row = Row::create(
            $this->entryFactory->create('entity_id', 789),
            $this->entryFactory->create('name', 'New Name'),
            $this->entryFactory->create($fromField, $validDate),
            $this->entryFactory->create(EtlCommonFieldNamesEnum::CHANGES_STORAGE, $changesStorage),
            $this->entryFactory->create(EtlCommonFieldNamesEnum::ENTITY_CACHE, $this->entityCache)
        );

        // Mock entity cache to throw NotFoundException
        $this->entityCache->method('get')
            ->with(['id' => 789], $entityClass)
            ->willThrowException(new NotFoundException('Entity not found'));

        // Mock entities provider to return empty array
        $this->entitiesByIdentifiersProvider->method('provide')
            ->willReturn([]);

        $this->logger->expects($this->once())
            ->method('debug')
            ->with('No entities found in validity period, skipping update');

        $result = $transformer->transform($row, $this->context);

        $this->assertSame($row, $result);
        $this->assertFalse($changesStorage->hasChanged());
    }

    public function testTransformWithNoChanges(): void
    {
        $fieldMap = ['name' => 'name'];
        $fromField = 'valid_date';
        $identifierFields = ['id' => 'entity_id'];
        $entityClass = 'TestEntity';

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->changePlanBuilder,
            $this->modifyHandler,
            $this->entitiesByIdentifiersProvider
        );
        $transformer->setLogger($this->logger);

        $entity = new \stdClass();
        $entity->name = 'Same Name';

        $changesStorage = new ObjectsChanges();
        $validDate = new \DateTime('2023-06-15');

        $row = Row::create(
            $this->entryFactory->create('entity_id', 999),
            $this->entryFactory->create('name', 'Same Name'),
            $this->entryFactory->create($fromField, $validDate),
            $this->entryFactory->create(EtlCommonFieldNamesEnum::CHANGES_STORAGE, $changesStorage),
            $this->entryFactory->create(EtlCommonFieldNamesEnum::ENTITY_CACHE, $this->entityCache)
        );

        // Mock entity cache to return entity
        $this->entityCache->method('get')
            ->with(['id' => 999], $entityClass)
            ->willReturn($this->createValidityBasedEntityMock([$entity]));

        // Mock change plan builder to return no changes
        $changePlan = $this->createMock(EntityChangePlan::class);
        $changePlan->method('hasChanges')->willReturn(false);

        $this->changePlanBuilder->method('reset')->willReturnSelf();
        $this->changePlanBuilder->method('setEntity')->willReturnSelf();
        $this->changePlanBuilder->method('setFieldMap')->willReturnSelf();
        $this->changePlanBuilder->method('setFields')->willReturnSelf();
        $this->changePlanBuilder->method('build')->willReturn($changePlan);

        $this->logger->expects($this->once())
            ->method('debug')
            ->with('No changes detected for entity, skipping');

        $result = $transformer->transform($row, $this->context);

        $this->assertSame($row, $result);
        $this->assertFalse($changesStorage->hasChanged());
    }

    public function testTransformWithMissingIdentifierField(): void
    {
        $fieldMap = ['name' => 'name'];
        $fromField = 'valid_date';
        $identifierFields = ['id' => 'missing_field'];
        $entityClass = 'TestEntity';

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->changePlanBuilder,
            $this->modifyHandler,
            $this->entitiesByIdentifiersProvider
        );

        $changesStorage = new ObjectsChanges();
        $validDate = new \DateTime('2023-06-15');

        $row = Row::create(
            $this->entryFactory->create('entity_id', 123),
            $this->entryFactory->create('name', 'New Name'),
            $this->entryFactory->create($fromField, $validDate),
            $this->entryFactory->create(EtlCommonFieldNamesEnum::CHANGES_STORAGE, $changesStorage),
            $this->entryFactory->create(EtlCommonFieldNamesEnum::ENTITY_CACHE, $this->entityCache)
        );

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Identifier field not found: id -> missing_field');

        $transformer->transform($row, $this->context);
    }

    private function createValidityBasedEntityMock(array $entities): object
    {
        $mock = new class($entities) {
            public function __construct(private array $entities) {}

            public function getAllByDateInterval(\DateTimeInterface $from, \DateTimeInterface $to): array
            {
                return $this->entities;
            }

            public function getByDay(\DateTimeInterface $day): array
            {
                return $this->entities;
            }
        };

        return $mock;
    }
}
